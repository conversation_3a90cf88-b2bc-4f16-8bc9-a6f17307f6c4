import { useEffect, useRef, useState } from 'react'
import { useForm, Controller } from "react-hook-form";
import PropTypes from 'prop-types';

import { getFormErrorMessage } from '@utils/helper'
import { useGlobalContext } from '@contexts/GlobalContext';
import {
  useCreateUserMutation,
  useUpdateUserMutation,
  useUploadMutation
} from '@quires';

import { classNames } from 'primereact/utils';
import { InputText } from 'primereact/inputtext';
import { Dropdown } from 'primereact/dropdown';
import { Password } from 'primereact/password';
import { Dialog } from 'primereact/dialog';
import { Button } from 'primereact/button';

import CustomFields from './CustomFields';

const typeOptions = [
  { label: "Employee", value: "employee" },
  { label: "Visitor", value: "visitor" },
];

const userTypeOptions = [
  { label: 'Admin', value: 'admin' },
  { label: 'Manager', value: 'manager' },
];

function AddMemberDialog({ actionType, data, onSuccess }) {
  const { openDialog, dialogHandler, disableBtn, setDisableBtn, userType, setOpenDialog } = useGlobalContext();
  const { control, formState: { errors }, handleSubmit, reset, setValue } = useForm();
  



  const uploadImage = useUploadMutation()
  const updateUser = useUpdateUserMutation();
  const addUser = useCreateUserMutation();

  const [companyUserType, setCompanyUserType] = useState("employee");
  const [image, setImage] = useState({});
  const [uploadedImageUrl, setUploadedImageUrl] = useState(null); // متغير جديد لحفظ رابط الصورة المرفوعة
  const [imagePreviewUrl, setImagePreviewUrl] = useState(null);
  const [originalImageUrl, setOriginalImageUrl] = useState(null);
  const [hasNewImage, setHasNewImage] = useState(false);
  const [imageUploading, setImageUploading] = useState(false); // حالة تحميل الصورة
  const hiddenFileInput = useRef(null);

  const [currentUserType] = useState(() => localStorage.getItem('user_type'));
  const isManager = currentUserType === 'manager';
  // const formUserType = watch('user_type');

  // Mobile detection
  const [isMobile, setIsMobile] = useState(window.innerWidth < 768);

  useEffect(() => {
    const handleResize = () => {
      setIsMobile(window.innerWidth < 768);
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  // تنظيف URL المعاينة عند إغلاق المكون
  useEffect(() => {
    return () => {
      if (imagePreviewUrl && imagePreviewUrl !== originalImageUrl) {
        URL.revokeObjectURL(imagePreviewUrl);
      }
    };
  }, [imagePreviewUrl, originalImageUrl]);

  const onClearImage = () => {
    setImage({});
    setHasNewImage(false);
    if (imagePreviewUrl && imagePreviewUrl !== originalImageUrl) {
      URL.revokeObjectURL(imagePreviewUrl);
    }
    setImagePreviewUrl(originalImageUrl || null);
    if (hiddenFileInput.current) {
      hiddenFileInput.current.value = "";
    }
  };

  const handleFileInputChange = async (event) => {
    const selectedFile = event.target.files[0];
    if (selectedFile) {
      setImage(selectedFile);
      setHasNewImage(true);
      if (imagePreviewUrl && imagePreviewUrl !== originalImageUrl) {
        URL.revokeObjectURL(imagePreviewUrl);
      }
      const previewUrl = URL.createObjectURL(selectedFile);
      setImagePreviewUrl(previewUrl);
      if (hiddenFileInput.current) {
        hiddenFileInput.current.value = "";
      }
      // رفع الصورة مباشرة عند اختيارها
      const formData = new FormData();
      formData.append("file", selectedFile);
      formData.append("file_type", "image");
      formData.append("user_id", localStorage.getItem("user_id"));
      setImageUploading(true); // ابدأ التحميل
      try {
        const res = await uploadImage.mutateAsync(formData);
        if (res?.file_url) {
          setUploadedImageUrl(res.file_url);
        } else {
          setUploadedImageUrl(null);
        }
      } catch (err) {
        setUploadedImageUrl(null);
      }
      setImageUploading(false); // انتهى التحميل
    }
  };

  const handleChooseFile = (e) => {
    e.preventDefault();
    e.stopPropagation();
    hiddenFileInput.current.click();
  };

  const createHandler = async (payload) => {
    setDisableBtn(true);

    // Save the current user type before making API calls
    const originalUserType = localStorage.getItem('user_type');
    // const originalUserRole = localStorage.getItem('user_role');

    try {
        // استخدم رابط الصورة الجديد إذا كان موجودًا
        if (uploadedImageUrl) {
          payload.image = uploadedImageUrl;
        }

        if (actionType !== "update" && isManager) {
            payload = {
                ...payload,
                email: ``,
                password: '',
                user_type: '',
                company_name: ''
            };
        }

        if (currentUserType !== "admin") {
            if (actionType === "update" && data) {
                payload.email = data.email || "";
                payload.password = data.password || "";
                payload.user_type = data.user_type || "";
            } else {
                delete payload.email;
                delete payload.password;
                delete payload.user_type;
            }
        }

        if (actionType !== "update") {
            delete payload.user_id;  
        }

        if (actionType === "update") {
            const userId = payload?.id;
            delete payload?.id;
            delete payload?.print_status;
            
            const updateData = {
                ...data,
                ...payload, 
                image: payload.image || data.image, 
            };
            
            updateUser.mutate(
                { id: userId, data: updateData },
                {
                    onSuccess: () => {
                        // Ensure user_type hasn't changed
                        if (localStorage.getItem('user_type') !== originalUserType) {
                            localStorage.setItem('user_type', originalUserType);
                        }
                        if (localStorage.getItem('user_role') !== "manager") {
                            localStorage.setItem('user_role', "manager");
                        }
                        
                        reset();
                        setDisableBtn(false);
                        
                        setImage({});
                        setUploadedImageUrl(null);
                        if (imagePreviewUrl && imagePreviewUrl !== originalImageUrl) {
                          URL.revokeObjectURL(imagePreviewUrl);
                        }
                        setImagePreviewUrl(null);
                        setOriginalImageUrl(null);
                        setHasNewImage(false);
                        
                        // Call onSuccess callback if provided
                        if (onSuccess) onSuccess();
                        
                        // Close dialog
                        setOpenDialog(prev => ({ ...prev, addMember: false }));
                    },
                    onError: (error) => {
                        console.error("Error updating user:", error);
                        setDisableBtn(false);
                        
                        // Also restore user type on error
                        if (localStorage.getItem('user_type') !== originalUserType) {
                            localStorage.setItem('user_type', originalUserType);
                        }
                    }
                }
            );
        } else {
            addUser.mutate(
                payload,
                {
                    onSuccess: () => {
        // Ensure user_type hasn't changed
        if (localStorage.getItem('user_type') !== originalUserType) {
            localStorage.setItem('user_type', originalUserType);
        }
        if (localStorage.getItem('user_role') !== "manager") {
            localStorage.setItem('user_role', "manager");
        }
        
        reset();
        setDisableBtn(false);
        
        setImage({});
        setUploadedImageUrl(null);
        if (imagePreviewUrl && imagePreviewUrl !== originalImageUrl) {
          URL.revokeObjectURL(imagePreviewUrl);
        }
        setImagePreviewUrl(null);
        setOriginalImageUrl(null);
        setHasNewImage(false);
        
                        // Call onSuccess callback if provided
        if (onSuccess) onSuccess();
                        
                        // Close dialog
                        setOpenDialog(prev => ({ ...prev, addMember: false }));
                    },
                    onError: (error) => {
                        console.error("Error creating user:", error);
                        setDisableBtn(false);
                        
                        // Also restore user type on error
                        if (localStorage.getItem('user_type') !== originalUserType) {
                            localStorage.setItem('user_type', originalUserType);
                        }
                    }
                }
            );
        }
        
    } catch (error) {
        console.error("Error saving data:", error);
        setDisableBtn(false);
        
        // Also restore user type on error
        if (localStorage.getItem('user_type') !== originalUserType) {
            localStorage.setItem('user_type', originalUserType);
        }
    }
};

  useEffect(() => {
    if (companyUserType === "employee") {
      setValue("department", data?.department || "");
    }
  }, [companyUserType, data, setValue]);

  useEffect(() => {
    if (actionType === "update") {
      setCompanyUserType(data?.type);
      reset(data);
      
      if (data?.image) {
        setOriginalImageUrl(data.image);
        setImagePreviewUrl(data.image);
        setHasNewImage(false);
        setImage({});
        setUploadedImageUrl(null); 
      } else {
        setOriginalImageUrl(null);
        setImagePreviewUrl(null);
        setHasNewImage(false);
        setImage({});
        setUploadedImageUrl(null);
      }
    } else {
      reset();
      setOriginalImageUrl(null);
      setImagePreviewUrl(null);
      setHasNewImage(false);
      setImage({});
      setUploadedImageUrl(null);
    }
  }, [actionType, data, reset]);

  return (
    <Dialog visible={openDialog.addMember}
      style={{
        width: isMobile ? '95vw' : '55%',
        maxHeight: '90vh'
      }}
      breakpoints={{
        '960px': '95vw',
        '641px': '95vw'
      }}
      header={`${actionType === "update" ? "Update User" : "Create User"}`}
      modal className="p-fluid"
      onHide={() => setOpenDialog(prev => ({ ...prev, addMember: false }))}
      contentStyle={{
        maxHeight: isMobile ? 'calc(90vh - 60px)' : 'auto',
        overflow: 'auto'
      }}
    >
<form onSubmit={handleSubmit(createHandler)} className="w-full flex flex-col justify-center">
  <div className={`col-full flex flex-wrap justify-start py-4 border-[gray] ${isMobile ? 'flex-col' : ''}`}>
    
    {/* Name */}
    <div className={`${isMobile ? 'w-full' : 'w-6/12'} mb-3 px-2`}>
      <div className="field ">
        <label className="form-label text-sm">
          Name <span style={{ color: 'red' }}>*</span>
        </label>

        <span className="p-float-label">
          <Controller name="name" control={control}
            rules={{ required: actionType === "update" ? false : 'Name is required.' }}
            render={({ field, fieldState }) => (
              <InputText
                id={field.name}
                {...field}
                ref={field.ref}
                className={`w-100  ${classNames({ 'p-invalid': fieldState.invalid })}`}
              />
            )} />
        </span>
        {getFormErrorMessage('name', errors)}
      </div>
    </div>





        {/* Phone */}
        <div className={`${isMobile ? 'w-full' : 'w-6/12'} mb-3 px-2`}>
      <div className="field ">
        <label className="form-label text-sm"> Phone <span style={{ color: 'red' }}>*</span> </label>
        <span className="p-float-label">
          <Controller name="phone" control={control}
            rules={{ required: actionType === "update" ? false : 'Phone is required.' }}
            render={({ field, fieldState }) => (
              <InputText
                id={field.name}
                {...field}
                ref={field.ref}
                className={`w-100  ${classNames({ 'p-invalid': fieldState.invalid })}`}
                keyfilter="num"
              />
            )} />
        </span>
        {getFormErrorMessage('phone', errors)}
      </div>
    </div>



    {/* Company Name */}
    {userType === "admin" &&
      <div className={`${isMobile ? 'w-full' : 'w-6/12'} mb-3 px-2`}>
        <div className="field">
          <label className="form-label text-sm">Company Name <span style={{ color: 'red' }}>*</span></label>
          <span className="p-float-label">
            <Controller name="company_name" control={control}
              rules={{ required: actionType === "update" ? false : "Company name is required!" }}
              render={({ field, fieldState }) => (
                <InputText
                  id={field.name}
                  {...field}
                  ref={field.ref}
                  className={`w-100 ${classNames({ 'p-invalid': fieldState.invalid })}`}
                />
              )}
            />
          </span>
          {getFormErrorMessage('company_name', errors)}
        </div>
      </div>
    }

            {/* Position */}
    <div className={`${isMobile ? 'w-full' : 'w-6/12'} mb-3 px-2`}>
      <div className="field ">
        <label className="form-label text-sm"> Position <span style={{ color: 'red' }}>*</span> </label>
        <span className="p-float-label">
          <Controller name="position" control={control}
            rules={{ required: actionType === "update" ? false : 'Position is required.' }}
            render={({ field, fieldState }) => (
              <InputText
                id={field.name}
                {...field}
                ref={field.ref}
                className={`w-100  ${classNames({ 'p-invalid': fieldState.invalid })}`}
              />
            )} />
        </span>
        {getFormErrorMessage('position', errors)}
      </div>
    </div>

 

     {/* Email */}
     {currentUserType === "admin" && (
      <div className={`${isMobile ? 'w-full' : 'w-6/12'} mb-3 px-2`}>
        <div className="field">
          <label className="form-label text-sm">Email <span style={{ color: 'red' }}>*</span></label>
          <span className="p-float-label">
            <Controller name="email" control={control}
              rules={{
                required: currentUserType === "admin" && actionType === "create" ? "Email is required!" : false,
                pattern: {
                  value: /^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/,
                  message: "Invalid email!",
                }
              }}
              render={({ field, fieldState }) => (
                <InputText
                  id={field.name}
                  {...field}
                  ref={field.ref}
                  className={`w-100 ${classNames({ 'p-invalid': fieldState.invalid })}`}
                />
              )}
            />
          </span>
          {getFormErrorMessage('email', errors)}
        </div>
      </div>
    )}

    {/* Password */}
    {currentUserType === "admin" && (
      <div className={`${isMobile ? 'w-full' : 'w-6/12'} mb-3 px-2`}>
        <div className="field">
          <label className="form-label text-sm">Password {actionType === "create" && <span style={{ color: 'red' }}>*</span>}</label>
          <span className="p-float-label">
            <Controller name="password" control={control}
              rules={{ required: currentUserType === "admin" && actionType === "create" ? "Password is required" : false }}
              render={({ field, fieldState }) => (
                <Password
                  id={field.name}
                  {...field}
                  ref={field.ref}
                  className={`w-100 ${classNames({ 'p-invalid': fieldState.invalid })}`}
                  toggleMask
                />
              )}
            />
          </span>
          {getFormErrorMessage('password', errors)}
        </div>
      </div>
    )}

    {/* Custom Fields */}
    <CustomFields
      control={control}
      errors={errors}
      setValue={setValue}
      companyUserType={companyUserType}
      setCompanyUserType={setCompanyUserType}
    />

   

    {/* User Type */}
    {currentUserType === "admin" && (
      <div className={`${isMobile ? 'w-full' : 'w-6/12'} mb-3 px-2`}>
        <label htmlFor="user_type" className='form-label text-sm'>User Type <span style={{ color: 'red' }}>*</span> </label>
        <Controller name="user_type" control={control}
          rules={{ required: currentUserType === "admin" && actionType === "create" ? 'User Type is required.' : false }}
          render={({ field, fieldState }) => (
            <Dropdown
              id={field.name} {...field}
              value={field.value}
              options={userTypeOptions}
              onChange={(e) => { field.onChange(e.value); }}
              optionLabel="label"
              optionValue="value"
              ref={field.ref}
              placeholder="select..."
              className={`w-100 ${classNames({ 'p-invalid': fieldState.invalid })}`}
            />
          )}
        />
        {getFormErrorMessage('user_type', errors)}
      </div>
    )}

    <div className={`${isMobile ? 'w-full' : 'w-6/12'} mb-3 px-2`}></div>
        {/* Image Input */}
    <div className={`${isMobile ? 'w-full' : 'w-11/12'} mb-3 px-2 ml-10`}>
      <label htmlFor="image" className="form-label text-sm mb-2 block font-medium text-gray-700">
        Profile Image
      </label>
      <Controller
        name="image"
        control={control}
        rules={{ required: false }}
        render={() => (
          <div className="image-upload-container bg-white rounded-xl shadow-sm">
            <input
              type="file"
              ref={hiddenFileInput}
              onChange={handleFileInputChange}
              accept="image/*"
              style={{ display: 'none' }}
            />
            <div 
              className="flex flex-col items-center justify-center p-8 transition-all duration-300 ease-in-out border-2 border-dashed border-blue-200 hover:border-blue-400 rounded-xl bg-gray-50 hover:bg-blue-50 cursor-pointer"
              onClick={handleChooseFile}
            >

              {imageUploading && (
                <div className="flex flex-col items-center justify-center mb-4">
                  <i className="pi pi-spin pi-spinner text-3xl text-blue-500"></i>
                  <span className="mt-2 text-blue-500">Uploading image...</span>
                </div>
              )}

              {!imagePreviewUrl ? (
                <>
                  <div className="mb-4 p-4 rounded-full bg-blue-100">
                    <i className="pi pi-image text-4xl text-blue-500"></i>
                  </div>
                  <span className="text-gray-700 font-medium mb-2">
                    Drag and drop your image here
                  </span>
                  <span className="text-sm text-gray-500 text-center">
                    or click to browse from your computer
                  </span>
                  <div className="mt-4 text-xs text-gray-400 flex items-center gap-2">
                    <i className="pi pi-info-circle"></i>
                    <span>Supported formats: JPG, PNG, GIF (Max: 2MB)</span>
                  </div>
                </>
              ) : (
                <div className="flex flex-col items-center p-4 w-full">
                  <div className="relative group mb-4">
                    <img
                      src={imagePreviewUrl}
                      alt="Preview"
                      className="w-40 h-40 object-cover rounded-lg shadow-md transition-transform duration-300 group-hover:scale-105"
                    />
                    <div className="absolute inset-0 bg-black bg-opacity-40 opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-lg flex items-center justify-center">
                      <div className="flex gap-3">
                        <Button
                          type="button"
                          icon="pi pi-times"
                          className="p-button-rounded p-button-danger p-button-sm !w-12 !h-12"
                          onClick={(e) => {
                            e.preventDefault();
                            e.stopPropagation();
                            onClearImage();
                          }}
                          tooltip="Remove image"
                        />
                        <Button
                          type="button"
                          icon="pi pi-upload"
                          className="p-button-rounded p-button-info p-button-sm !w-12 !h-12"
                          onClick={handleChooseFile}
                          tooltip="Change image"
                        />
                      </div>
                    </div>
                  </div>
                  <div className="text-center">
                    <h4 className="text-sm font-medium text-gray-700 mb-1">Current Image</h4>
                    <p className="text-xs text-gray-500">Hover to edit or remove</p>
                  </div>
                </div>
              )}
            </div>
            {hasNewImage && image.name && (
              <div className="mt-3 flex items-center gap-2 p-3 bg-green-50 rounded-lg border border-green-100">
                <i className="pi pi-check-circle text-green-500"></i>
                <div className="flex flex-col">
                  <span className="text-sm font-medium text-green-700">Image Selected</span>
                  <span className="text-xs text-green-600">{image.name}</span>
                </div>
              </div>
            )}
          </div>
        )}
      />
    </div>

    {/* Buttons */}
    <div className={`${isMobile ? 'w-full' : 'w-6/12'} mb-3 px-2`}>
      <Button label="Cancel"
        className="w-full"
        onClick={() => setOpenDialog(prev => ({ ...prev, addMember: false }))}
      />
    </div>
    <div className={`${isMobile ? 'w-full' : 'w-6/12'} mb-3 px-2`}>
      <Button label={actionType === "update" ? "Update User" : "Create User"}
        className="w-full"
        loading={disableBtn || imageUploading}
        disabled={disableBtn || imageUploading}
        onClick={handleSubmit(createHandler)}
      />
    </div>
  </div>
</form>
</Dialog>
  );
}

AddMemberDialog.propTypes = {
  actionType: PropTypes.oneOf(['create', 'update']).isRequired,
  data: PropTypes.shape({
    id: PropTypes.string,
    name: PropTypes.string,
    email: PropTypes.string,
    password: PropTypes.string,
    phone: PropTypes.string,
    position: PropTypes.string,
    department: PropTypes.string,
    type: PropTypes.string,
    image: PropTypes.string,
    user_type: PropTypes.string,
    company_name: PropTypes.string,
    print_status: PropTypes.bool,
    custom_field_1: PropTypes.string,
    custom_field_2: PropTypes.string,
    custom_field_3: PropTypes.string,
    custom_field_4: PropTypes.string,
    custom_field_5: PropTypes.string,
    custom_field_6: PropTypes.string,
    custom_field_7: PropTypes.string,
    custom_field_8: PropTypes.string,
    custom_field_9: PropTypes.string,
    custom_field_10: PropTypes.string,
  }),
  onSuccess: PropTypes.func,
};

export default AddMemberDialog;